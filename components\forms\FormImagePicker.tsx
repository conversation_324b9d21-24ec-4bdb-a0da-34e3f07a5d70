import React, { useEffect, useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Image,
  StyleProp,
  ViewStyle,
  Alert,
  ScrollView,
} from "react-native";
import {
  useController,
  Control,
  FieldValues,
  Path,
  useFormContext,
} from "react-hook-form";
import * as ImagePicker from "expo-image-picker";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";
import { useCreateSignedUploadUrlMutation } from "@/generated/graphql";

interface FormImagePickerProps<T extends FieldValues> {
  name: Path<T>;
  control: Control<T>;
  label?: string;
  placeholder?: string;
  containerStyle?: StyleProp<ViewStyle>;
  rules?: object;
}

export type FileState = {
  file: File;
  progress: number;
  uploadedUrl?: string;
};

// Add a helper function to extract filename from URL
const getFileNameFromUrl = (url: string) => {
  try {
    const urlPath = new URL(url).pathname;
    return decodeURIComponent(urlPath.split("/").pop() || "");
  } catch {
    return "Unknown file";
  }
};
export function FormImagePicker<T extends FieldValues>({
  name,
  control,
  label,
  placeholder = "Take a photo or upload",
  containerStyle,
  rules,
}: FormImagePickerProps<T>) {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
    rules,
  });

  const methods = useFormContext();
  const formValue = methods.watch(name);

  const images: string[] = field.value || [];
  const [files, setFiles] = useState<FileState[]>([]);
  const { mutateAsync: getSignedUrl } = useCreateSignedUploadUrlMutation();

  const handlePickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== "granted") {
      Alert.alert("Permission Required", "We need access to your gallery.");
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ["images"],
      allowsMultipleSelection: true,
      selectionLimit: 0, // 0 means no limit, allows multiple selection
      allowsEditing: false,
      quality: 0.8,
    });

    if (!result.canceled) {
      const uris = result.assets.map((asset) => asset.uri);
      field.onChange([...images, ...uris]);
    }
  };

  const handleTakePhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== "granted") {
      Alert.alert("Permission Required", "We need camera access.");
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ["images"],
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled) {
      field.onChange([...images, result.assets[0].uri]);
    }
  };

  const handleRemoveImage = (uriToRemove: string) => {
    field.onChange(images.filter((uri) => uri !== uriToRemove));
  };

  const showImageSourceOptions = () => {
    Alert.alert("Add Image", "Choose an option", [
      { text: "Take Photo", onPress: handleTakePhoto },
      { text: "Choose from Gallery", onPress: handlePickImage },
      { text: "Cancel", style: "cancel" },
    ]);
  };
  useEffect(() => {
    if (formValue && typeof formValue === "string" && files.length === 0) {
      // Create a mock file state for existing URL
      setFiles([
        {
          file: new File([], getFileNameFromUrl(formValue)), // Empty file with name from URL
          progress: 100,
          uploadedUrl: formValue,
        },
      ]);
    } else if (!formValue) {
      setFiles([]);
    }
  }, [formValue]);

  const uploadFile = async (file: File) => {
    try {
      // Get signed URL with fields
      const data = await getSignedUrl({
        input: {
          key: `uploads/${Date.now()}-${file.name}`,
          contentType: file.type, // This should match exactly what S3 expects
          expiresIn: 300,
        },
      });

      if (
        !data?.createSignedUploadUrl?.url ||
        !data?.createSignedUploadUrl?.fields
      ) {
        throw new Error("Failed to get upload URL");
      }

      // Create FormData with fields in specific order
      const formData = new FormData();
      const fields = data.createSignedUploadUrl.fields;

      // Add fields in correct order for S3
      formData.append("key", fields.key);
      formData.append("bucket", fields.bucket);
      formData.append("acl", fields.acl);
      formData.append("X-Amz-Algorithm", fields.algorithm);
      formData.append("X-Amz-Credential", fields.credential);
      formData.append("X-Amz-Date", fields.date);
      formData.append("Policy", fields.Policy);
      formData.append("X-Amz-Signature", fields.signature);

      // Important: Use the exact Content-Type from the policy
      formData.append("Content-Type", file.type); // Use file's content type

      // Add the file last
      formData.append("file", file);

      // Upload using XMLHttpRequest
      const xhr = new XMLHttpRequest();
      xhr.open("POST", data.createSignedUploadUrl.url);

      // Don't set any content type header, let the browser set it with the form data
      return new Promise((resolve, reject) => {
        xhr.upload.addEventListener("progress", (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            setFiles((current) =>
              current.map((f) => (f.file === file ? { ...f, progress } : f))
            );
          }
        });

        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            // Construct the final S3 URL
            const fileUrl = `${data.createSignedUploadUrl.url}/${fields.key}`;
            resolve(fileUrl);
          } else {
            reject(new Error("Upload failed"));
          }
        };

        xhr.onerror = () => reject(new Error("Upload failed"));
        xhr.send(formData);
      });
    } catch (error) {
      console.error("Upload failed:", error);
      throw error;
    }
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && <Text style={styles.label}>{label}</Text>}
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {images.map((uri) => (
          <View key={uri} style={styles.imageWrapper}>
            <Image source={{ uri }} style={styles.image} />
            <TouchableOpacity
              style={styles.removeButton}
              onPress={() => handleRemoveImage(uri)}
            >
              <Ionicons name="close-circle" size={20} color="red" />
            </TouchableOpacity>
          </View>
        ))}
        <TouchableOpacity
          style={styles.addImageButton}
          onPress={showImageSourceOptions}
        >
          <Ionicons
            name="add-circle-outline"
            size={40}
            color={Colors.primary}
          />
          <Text style={styles.uploadText}>{placeholder}</Text>
        </TouchableOpacity>
      </ScrollView>
      {error && <Text style={styles.errorText}>{error.message}</Text>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    color: Colors.text,
    marginBottom: 8,
  },
  imageWrapper: {
    position: "relative",
    marginRight: 10,
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  removeButton: {
    position: "absolute",
    top: -5,
    right: -5,
    backgroundColor: "white",
    borderRadius: 10,
  },
  addImageButton: {
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 12,
  },
  uploadText: {
    marginTop: 4,
    fontSize: 12,
    color: Colors.primary,
    textAlign: "center",
  },
  errorText: {
    fontSize: 12,
    color: Colors.error || "red",
    marginTop: 4,
  },
});
